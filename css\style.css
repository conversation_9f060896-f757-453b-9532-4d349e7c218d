/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo a {
    font-size: 1.8rem;
    font-weight: 800;
    color: #6366f1;
    text-decoration: none;
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-link {
    color: #333;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: #6366f1;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
}

.hero h1 {
    font-size: 4rem;
    font-weight: 800;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #fff, #f0f0f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: fadeInUp 1s ease;
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    animation: fadeInUp 1s ease 0.2s both;
}

.hero-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
    animation: fadeInUp 1s ease 0.4s both;
}

.hero-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    text-align: left;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease;
}

.hero-card:hover {
    transform: translateY(-10px);
}

.hero-card img {
    width: 80px;
    height: 80px;
    border-radius: 15px;
    margin-bottom: 1rem;
    object-fit: cover;
}

.hero-card h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.hero-card p {
    opacity: 0.8;
    line-height: 1.6;
}

/* About Section */
.about {
    padding: 5rem 0;
    background: #f8fafc;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text h2 {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.about-text .subtitle {
    color: #6366f1;
    font-weight: 600;
    margin-bottom: 1rem;
}

.about-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #64748b;
    margin-bottom: 2rem;
}

.tags {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.tag {
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: transform 0.3s ease;
}

.tag:hover {
    transform: translateY(-2px);
}

/* Services Section */
.services {
    padding: 5rem 0;
    background: white;
}

.section-title {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title h2 {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.section-title p {
    font-size: 1.2rem;
    color: #64748b;
    max-width: 600px;
    margin: 0 auto;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.service-card {
    background: #f8fafc;
    padding: 2rem;
    border-radius: 20px;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.service-card:hover {
    transform: translateY(-10px);
    border-color: #6366f1;
    background: white;
    box-shadow: 0 20px 40px rgba(99, 102, 241, 0.1);
}

.service-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #333;
}

/* Portfolio Section */
.portfolio {
    padding: 5rem 0;
    background: #f8fafc;
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 3rem;
    margin-top: 3rem;
}

.portfolio-item {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.portfolio-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.portfolio-image img {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

.portfolio-content {
    padding: 2rem;
}

.portfolio-tags {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.portfolio-tags .tag {
    font-size: 0.8rem;
    padding: 0.3rem 0.8rem;
}

.portfolio-content h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #6366f1;
    margin-bottom: 0.5rem;
}

.portfolio-content h4 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #333;
}

.portfolio-content p {
    color: #64748b;
    line-height: 1.6;
}

/* Timeline Section */
.timeline {
    padding: 5rem 0;
    background: white;
}

.timeline-items {
    margin-top: 3rem;
    position: relative;
}

.timeline-items::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    transform: translateX(-50%);
}

.timeline-item {
    display: flex;
    justify-content: center;
    margin-bottom: 3rem;
    position: relative;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 20px;
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    border-radius: 50%;
    transform: translateX(-50%);
    z-index: 2;
}

.timeline-content {
    background: #f8fafc;
    padding: 2rem;
    border-radius: 15px;
    max-width: 400px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.timeline-content h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #333;
}

.timeline-content p {
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.timeline-date {
    color: #6366f1;
    font-weight: 600;
    font-size: 0.9rem;
}

/* Gallery Section */
.gallery {
    padding: 5rem 0;
    background: #f8fafc;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 3rem;
}

.gallery-grid img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    border-radius: 15px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.gallery-grid img:hover {
    transform: scale(1.05);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

/* Contact Section */
.contact {
    padding: 5rem 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.contact-text h2 {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 2rem;
}

.contact-info h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.contact-info p {
    opacity: 0.9;
    line-height: 1.6;
}

.contact-form {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: none;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    background: white;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
}

.btn-submit {
    width: 100%;
    padding: 1rem;
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(99, 102, 241, 0.3);
}

/* Footer */
.footer {
    background: #1e293b;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.footer-logo h3 {
    font-size: 1.5rem;
    font-weight: 800;
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.footer-links {
    display: flex;
    gap: 2rem;
}

.footer-links a {
    color: #94a3b8;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: white;
}

.footer-social {
    display: flex;
    gap: 1rem;
}

.footer-social a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.footer-social a:hover {
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    transform: translateY(-2px);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #334155;
    color: #94a3b8;
}

.footer-bottom a {
    color: #6366f1;
    text-decoration: none;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .hero-cards {
        grid-template-columns: 1fr;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .section-title h2 {
        font-size: 2rem;
    }

    .portfolio-grid {
        grid-template-columns: 1fr;
    }

    .timeline-items::before {
        left: 20px;
    }

    .timeline-item {
        justify-content: flex-start;
        padding-left: 50px;
    }

    .timeline-item::before {
        left: 20px;
    }

    .timeline-content {
        text-align: left;
    }

    .gallery-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-text h2 {
        font-size: 2rem;
    }

    .footer-content {
        flex-direction: column;
        gap: 2rem;
        text-align: center;
    }

    .footer-links {
        order: 2;
    }

    .footer-social {
        order: 3;
    }
}
