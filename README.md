# Artiflo - Creative Portfolio Website

A modern, responsive portfolio website built with PHP, inspired by the Artiflo Webflow template. This website features a vibrant design with smooth animations, perfect for creative professionals and agencies.

## Features

- **Modern Design**: Clean, vibrant design with gradient backgrounds and smooth animations
- **Responsive Layout**: Fully responsive design that works on all devices
- **Smooth Scrolling**: Smooth navigation between sections
- **Contact Form**: Working PHP contact form with validation
- **Portfolio Gallery**: Interactive gallery with lightbox effect
- **Timeline Section**: Visual timeline for showcasing experience
- **Service Cards**: Animated service cards with hover effects
- **Social Media Integration**: Links to social media profiles

## Sections

1. **Hero Section**: Eye-catching header with animated cards
2. **About Section**: Personal introduction with tags and description
3. **Services Section**: Grid of services offered
4. **Portfolio Section**: Showcase of work with project details
5. **Timeline Section**: Professional journey timeline
6. **Gallery Section**: Visual portfolio gallery
7. **Contact Section**: Contact form and information
8. **Footer**: Links and social media

## Installation

1. **Requirements**:
   - PHP 7.4 or higher
   - Web server (Apache/Nginx)
   - Mail server configuration (for contact form)

2. **Setup**:
   ```bash
   # Clone or download the files to your web server directory
   # For XAMPP users, place in htdocs folder
   # For other servers, place in your web root directory
   ```

3. **Configuration**:
   - Edit `config.php` to update site settings
   - Configure email settings for the contact form
   - Update social media links in the config file

4. **Customization**:
   - Replace placeholder images in the `images/` directory
   - Update content in `index.php`
   - Modify colors and styles in `css/style.css`

## File Structure

```
artiflo/
├── index.php              # Main homepage
├── config.php             # Configuration settings
├── contact-handler.php    # Contact form processor
├── includes/
│   ├── header.php         # Header and navigation
│   └── footer.php         # Footer section
├── css/
│   └── style.css          # Main stylesheet
├── js/
│   └── script.js          # JavaScript functionality
├── images/                # Image assets directory
└── README.md              # This file
```

## Customization Guide

### Colors
The website uses a modern color palette with gradients. Main colors:
- Primary: `#6366f1` (Indigo)
- Secondary: `#8b5cf6` (Purple)
- Accent: `#ec4899` (Pink)

### Fonts
- Primary font: Inter (Google Fonts)
- Font weights: 300, 400, 500, 600, 700, 800

### Images
Replace the placeholder images with your own:
- Hero section images (80x80px recommended)
- Portfolio project images (400x300px recommended)
- Gallery images (300x300px recommended)
- About section image (500x600px recommended)

### Content
Update the following in `index.php`:
- Site name and tagline
- About section text
- Services list
- Portfolio projects
- Timeline events
- Contact information

## Contact Form Setup

The contact form requires proper email configuration:

1. **Basic Setup**: The form uses PHP's `mail()` function
2. **SMTP Setup**: For better reliability, consider using PHPMailer with SMTP
3. **Database Storage**: Uncomment the database code in `contact-handler.php` to store submissions

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers

## License

This template is free to use for personal and commercial projects. Attribution is appreciated but not required.

## Credits

- Inspired by Artiflo Webflow template
- Built with modern web technologies
- Icons from Font Awesome
- Fonts from Google Fonts

## Support

For questions or issues, please check the code comments or create an issue in the repository.
