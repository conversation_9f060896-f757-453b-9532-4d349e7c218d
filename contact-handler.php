<?php
require_once 'config.php';

// Set content type to JSON
header('Content-Type: application/json');

// Initialize response array
$response = array('success' => false, 'message' => '');

// Check if form was submitted via POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['message'] = 'Invalid request method';
    echo json_encode($response);
    exit;
}

// Sanitize and validate input data
$name = isset($_POST['name']) ? trim($_POST['name']) : '';
$email = isset($_POST['email']) ? trim($_POST['email']) : '';
$message = isset($_POST['message']) ? trim($_POST['message']) : '';

// Validation
$errors = array();

if (empty($name)) {
    $errors[] = 'Name is required';
} elseif (strlen($name) < 2) {
    $errors[] = 'Name must be at least 2 characters long';
}

if (empty($email)) {
    $errors[] = 'Email is required';
} elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    $errors[] = 'Please enter a valid email address';
}

if (empty($message)) {
    $errors[] = 'Message is required';
} elseif (strlen($message) < 10) {
    $errors[] = 'Message must be at least 10 characters long';
}

// If there are validation errors
if (!empty($errors)) {
    $response['message'] = implode(', ', $errors);
    echo json_encode($response);
    exit;
}

// Sanitize data
$name = htmlspecialchars($name, ENT_QUOTES, 'UTF-8');
$email = htmlspecialchars($email, ENT_QUOTES, 'UTF-8');
$message = htmlspecialchars($message, ENT_QUOTES, 'UTF-8');

// Prepare email content
$to = ADMIN_EMAIL;
$subject = 'New Contact Form Submission from ' . SITE_NAME;
$email_body = "
New contact form submission from " . SITE_NAME . "

Name: {$name}
Email: {$email}
Message:
{$message}

---
Sent from: " . $_SERVER['HTTP_HOST'] . "
IP Address: " . $_SERVER['REMOTE_ADDR'] . "
User Agent: " . $_SERVER['HTTP_USER_AGENT'] . "
Date: " . date('Y-m-d H:i:s') . "
";

// Email headers
$headers = array(
    'From: ' . CONTACT_EMAIL,
    'Reply-To: ' . $email,
    'X-Mailer: PHP/' . phpversion(),
    'Content-Type: text/plain; charset=UTF-8'
);

// Attempt to send email
try {
    $mail_sent = mail($to, $subject, $email_body, implode("\r\n", $headers));
    
    if ($mail_sent) {
        $response['success'] = true;
        $response['message'] = 'Thank you! Your message has been sent successfully.';
        
        // Optional: Log successful submissions
        error_log("Contact form submission from: {$name} ({$email})");
        
    } else {
        $response['message'] = 'Sorry, there was an error sending your message. Please try again later.';
        error_log("Failed to send contact form email from: {$name} ({$email})");
    }
    
} catch (Exception $e) {
    $response['message'] = 'Sorry, there was an error sending your message. Please try again later.';
    error_log("Contact form error: " . $e->getMessage());
}

// Optional: Save to database (uncomment if you want to store submissions)
/*
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $stmt = $pdo->prepare("INSERT INTO contact_submissions (name, email, message, submitted_at, ip_address) VALUES (?, ?, ?, NOW(), ?)");
    $stmt->execute([$name, $email, $message, $_SERVER['REMOTE_ADDR']]);
    
} catch (PDOException $e) {
    error_log("Database error: " . $e->getMessage());
}
*/

// Return JSON response
echo json_encode($response);
exit;
?>
